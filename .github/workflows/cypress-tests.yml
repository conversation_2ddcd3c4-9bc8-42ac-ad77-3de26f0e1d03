name: Cypress Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  # Cypress configuration
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
  # Performance thresholds
  PERFORMANCE_THRESHOLD: 5000
  # Test data
  TEST_ENVIRONMENT: staging

jobs:
  # Lint and format check
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint || echo "Linting issues found"

      - name: Run Prettier check
        run: npm run prettier:format || echo "Formatting issues found"

  # Unit tests (if any)
  unit-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm test || echo "No unit tests configured"

  # API Tests
  api-tests:
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      fail-fast: false
      matrix:
        # Test different browsers
        browser: [chrome, firefox, edge]
        # Test different viewports
        viewport: [desktop, tablet, mobile]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run API tests
        run: npm run api:tests
        env:
          # Set viewport based on matrix
          CYPRESS_VIEWPORT_WIDTH: ${{ matrix.viewport == 'mobile' && '375' || matrix.viewport == 'tablet' && '768' || '1920' }}
          CYPRESS_VIEWPORT_HEIGHT: ${{ matrix.viewport == 'mobile' && '667' || matrix.viewport == 'tablet' && '1024' || '1080' }}

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: api-test-results-${{ matrix.browser }}-${{ matrix.viewport }}
          path: cypress/reports/
          retention-days: 30

  # UI Tests
  ui-tests:
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      fail-fast: false
      matrix:
        browser: [chrome, firefox, edge]
        viewport: [desktop, tablet, mobile]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run UI tests
        run: npm run ui:tests
        env:
          CYPRESS_VIEWPORT_WIDTH: ${{ matrix.viewport == 'mobile' && '375' || matrix.viewport == 'tablet' && '768' || '1920' }}
          CYPRESS_VIEWPORT_HEIGHT: ${{ matrix.viewport == 'mobile' && '667' || matrix.viewport == 'tablet' && '1024' || '1080' }}

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: ui-test-results-${{ matrix.browser }}-${{ matrix.viewport }}
          path: cypress/reports/
          retention-days: 30

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: screenshots-${{ matrix.browser }}-${{ matrix.viewport }}
          path: cypress/screenshots/
          retention-days: 30

      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: videos-${{ matrix.browser }}-${{ matrix.viewport }}
          path: cypress/videos/
          retention-days: 30

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [api-tests, ui-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run integration tests
        run: npm run test:all -- --spec "cypress/e2e/integration/**/*.cy.js"

      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: cypress/reports/
          retention-days: 30

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: npm run test:all -- --env grepTags="@Performance"
        env:
          PERFORMANCE_THRESHOLD: ${{ env.PERFORMANCE_THRESHOLD }}

      - name: Upload performance test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: cypress/reports/
          retention-days: 30

  # Accessibility Tests
  accessibility-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run accessibility tests
        run: npm run test:all -- --env grepTags="@Accessibility"

      - name: Upload accessibility test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-test-results
          path: cypress/reports/
          retention-days: 30

  # Visual Regression Tests
  visual-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run visual regression tests
        run: npm run test:all -- --env grepTags="@Visual"

      - name: Upload visual test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: visual-test-results
          path: cypress/screenshots/
          retention-days: 30

  # Security Tests
  security-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security tests
        run: npm run test:all -- --env grepTags="@Security"

      - name: Upload security test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-test-results
          path: cypress/reports/
          retention-days: 30

  # Generate and publish test reports
  generate-reports:
    runs-on: ubuntu-latest
    needs:
      [
        api-tests,
        ui-tests,
        integration-tests,
        performance-tests,
        accessibility-tests,
        visual-tests,
        security-tests
      ]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          path: cypress/reports/

      - name: Generate combined report
        run: |
          # Combine all test results
          echo "Generating combined test report..."
          # Add logic to combine reports from different test types

      - name: Upload combined report
        uses: actions/upload-artifact@v4
        with:
          name: combined-test-report
          path: cypress/reports/
          retention-days: 90

      - name: Publish test results
        uses: actions/upload-artifact@v4
        if: github.event_name == 'pull_request'
        with:
          name: test-results-${{ github.run_number }}
          path: cypress/reports/
          retention-days: 90

  # Notify on test completion
  notify:
    runs-on: ubuntu-latest
    needs: [generate-reports]
    if: always()
    steps:
      - name: Notify test completion
        run: |
          echo "All tests completed!"
          # Add notification logic (Slack, email, etc.)
          # Example: curl -X POST -H 'Content-type: application/json' --data '{"text":"Tests completed!"}' $SLACK_WEBHOOK_URL

  # Deploy to staging (if tests pass)
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [api-tests, ui-tests, integration-tests]
    if: github.ref == 'refs/heads/develop' && success()
    environment: staging
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add deployment logic here

  # Deploy to production (if tests pass)
  deploy-production:
    runs-on: ubuntu-latest
    needs:
      [
        api-tests,
        ui-tests,
        integration-tests,
        performance-tests,
        accessibility-tests,
        security-tests
      ]
    if: github.ref == 'refs/heads/main' && success()
    environment: production
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add deployment logic here
