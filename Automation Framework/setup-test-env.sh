#!/bin/bash

# Setup script for Cypress testing environment
# This script sets up the environment to avoid rate limiting issues

echo "🚀 Setting up Cypress testing environment..."

# Set environment variables for testing
export NODE_ENV=test
export CYPRESS_TESTING=true

# Set Cypress environment variables
export CYPRESS_BASE_URL=http://localhost:3000
export CYPRESS_TODO_API_URL=http://localhost:3001/api
export CYPRESS_TODO_APP_URL=http://localhost:3000

echo "✅ Environment variables set:"
echo "   NODE_ENV: $NODE_ENV"
echo "   CYPRESS_TESTING: $CYPRESS_TESTING"
echo "   CYPRESS_BASE_URL: $CYPRESS_BASE_URL"
echo "   CYPRESS_TODO_API_URL: $CYPRESS_TODO_API_URL"

echo ""
echo "📝 To run tests with this configuration:"
echo "   1. Start the backend: npm run start:backend"
echo "   2. Start the frontend: npm run start:frontend"
echo "   3. Run tests: npm run cy:run"
echo ""
echo "🔧 Or run the full setup: npm run dev" 