{"name": "cypress-todo-app-assignment", "version": "1.0.0", "description": "A full-stack Todo application with React frontend, Node.js backend, and comprehensive Cypress testing framework", "main": "index.js", "workspaces": ["Todo App/frontend", "Todo App/backend"], "scripts": {"prettier-format": "prettier --config \"Automation Framework/.prettierrc\" '**/*.js' --write", "cy:verify": "cd \"Automation Framework\" && cypress verify", "cy:run": "cd \"Automation Framework\" && cypress run", "cy:open": "cd \"Automation Framework\" && cypress open", "e2e:smoke:tests": "cd \"Automation Framework\" && cypress run --env grepTags=@Smoke,grepFilterSpecs=true", "e2e:regression:tests": "cd \"Automation Framework\" && cypress run --env grepTags=@Regression,grepFilterSpecs=true", "api:tests": "cd \"Automation Framework\" && cypress run --spec 'cypress/e2e/api/**/*.cy.js'", "ui:tests": "cd \"Automation Framework\" && cypress run --spec 'cypress/e2e/ui/**/*.cy.js'", "test:all": "cd \"Automation Framework\" && cypress run", "test:headed": "cd \"Automation Framework\" && cypress run --headed", "test:chrome": "cd \"Automation Framework\" && cypress run --browser chrome", "test:firefox": "cd \"Automation Framework\" && cypress run --browser firefox", "test:edge": "cd \"Automation Framework\" && cypress run --browser edge", "test:mobile": "cd \"Automation Framework\" && cypress run --config viewportWidth=375,viewportHeight=667", "test:tablet": "cd \"Automation Framework\" && cypress run --config viewportWidth=768,viewportHeight=1024", "test:desktop": "cd \"Automation Framework\" && cypress run --config viewportWidth=1920,viewportHeight=1080", "test:parallel": "cd \"Automation Framework\" && cypress run --parallel --record", "start:backend": "cd \"Todo App/backend\" && npm start", "start:frontend": "cd \"Todo App/frontend\" && npm start", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start": "npm run dev", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd \"Todo App/backend\" && npm install", "install:frontend": "cd \"Todo App/frontend\" && npm install"}, "repository": {"type": "git", "url": "git+https://github.com/aashir1998/Cypress-Todo-App-Assignment.git"}, "keywords": ["cypress", "testing", "automation", "e2e", "api", "todo-app", "react", "nodejs", "express"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/aashir1998/Cypress-Todo-App-Assignment/issues"}, "homepage": "https://github.com/aashir1998/Cypress-Todo-App-Assignment#readme", "devDependencies": {"@cypress/grep": "^4.1.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.9.0", "concurrently": "^9.2.0", "cypress": "^14.5.3", "cypress-mochawesome-reporter": "^3.8.2", "cypress-plugin-api": "^2.11.2", "cypress-recurse": "^1.35.3", "cypress-terminal-report": "^7.2.1", "cypress-visual-regression": "^5.3.0", "dotenv": "^16.4.7", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-cypress": "^4.1.0", "globals": "^15.15.0", "import": "^0.0.6", "prettier": "3.5.1"}}